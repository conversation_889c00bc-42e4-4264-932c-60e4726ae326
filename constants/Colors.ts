type ColorScheme = {
  primary: string;
  text: string;
  border: string;
  secondary: string;
  background: string;
  icon: string;
  tabIconDefault: string;
  linearGradient: [string, string];
  inputBackground: string;
  inputPlaceholderColor: string;
  inputIconColor: string;
  desactivated: string;
};

export type AppTheme =
  | 'lightOrange'
  | 'lightRed'
  | 'lightPurple'
  | 'lightBlue'
  | 'lightGreen'
  | 'darkOrange'
  | 'darkRed'
  | 'darkPurple'
  | 'darkBlue'
  | 'darkGreen';
export type AppThemeColors = Record<AppTheme, ColorScheme>;

// Helper function to safely get colors with fallback
export const getThemeColors = (theme?: AppTheme | null): ColorScheme => {
  const safeTheme = theme || 'lightBlue';
  return Colors[safeTheme];
};

export const Colors: AppThemeColors = {
  lightOrange: {
    primary: '#E97633',
    text: '#000000',
    border: '#E9763380',
    secondary: '#EBD30233',
    background: '#FFFFFF',
    icon: '#000000',
    tabIconDefault: '#A2A2A2',
    linearGradient: ['#FFB71B', '#FFB71B00'],
    inputBackground: '#F8F8F8',
    inputPlaceholderColor: '#7E7E7E80',
    inputIconColor: '#9B9B9B',
    desactivated: '#E7E7E7',
  },
  lightRed: {
    primary: ' #C84279',
    text: '#000000',
    border: '#C8427980',
    secondary: '#FF4E0D1A',
    background: '#FFFFFF',
    icon: '#000000',
    tabIconDefault: '#A2A2A2',
    linearGradient: ['#EFAEC5', '#FAE5DD'],
    inputBackground: '#F8F8F8',
    inputPlaceholderColor: '#7E7E7E80',
    inputIconColor: '#9B9B9B',
    desactivated: '#E7E7E7',
  },
  lightPurple: {
    primary: '#9F40F3',
    text: '#000000',
    border: '#9F40F380',
    secondary: '#FF3DF21A',
    background: '#FFFFFF',
    icon: '#000000',
    tabIconDefault: '#A2A2A2',
    linearGradient: ['#C3B9EF', '#F9E6F2'],
    inputBackground: '#F8F8F8',
    inputPlaceholderColor: '#7E7E7E80',
    inputIconColor: '#9B9B9B',
    desactivated: '#E7E7E7',
  },
  lightBlue: {
    primary: '#3674D7',
    text: '#000000',
    border: '#3674D780',
    secondary: '#09EA9F40',
    background: '#FFFFFF',
    icon: '#000000',
    tabIconDefault: '#A2A2A2',
    linearGradient: ['#A1F2E0', '#CFF9E9'],
    inputBackground: '#F8F8F8',
    inputPlaceholderColor: '#7E7E7E80',
    inputIconColor: '#9B9B9B',
    desactivated: '#E7E7E7',
  },
  lightGreen: {
    primary: '#078566',
    text: '#000000',
    border: '#07856680',
    secondary: '#78E70030',
    background: '#FFFFFF',
    icon: '#000000',
    tabIconDefault: '#A2A2A2',
    linearGradient: ['#B0EA99', '#DBF5C1'],
    inputBackground: '#F8F8F8',
    inputPlaceholderColor: '#7E7E7E80',
    inputIconColor: '#9B9B9B',
    desactivated: '#E7E7E7',
  },
  darkOrange: {
    primary: '#E97633',
    text: '#FFFFFF',
    border: '#E9763380',
    secondary: '#E9763333',
    background: '#000000',
    icon: '#FFFFFF',
    tabIconDefault: '#A2A2A2',
    linearGradient: ['#6A3C21', '#764122'],
    inputBackground: '#1E1E1E',
    inputPlaceholderColor: '#B0B0B080',
    inputIconColor: '#CCCCCC',
    desactivated: '#9B9B9B',
  },
  darkRed: {
    primary: '#C84279',
    text: '#FFFFFF',
    border: '#C8427980',
    secondary: '#C8427933',
    background: '#000000',
    icon: '#FFFFFF',
    tabIconDefault: '#A2A2A2',
    linearGradient: ['#5C273C', '#662841'],
    inputBackground: '#1E1E1E',
    inputPlaceholderColor: '#B0B0B080',
    inputIconColor: '#CCCCCC',
    desactivated: '#9B9B9B',
  },
  darkPurple: {
    primary: '#9F40F3',
    text: '#FFFFFF',
    border: '#9F40F380',
    secondary: '#9F40F333',
    background: '#000000',
    icon: '#FFFFFF',
    tabIconDefault: '#A2A2A2',
    linearGradient: ['#4D2D6D', '#56337B'],
    inputBackground: '#1E1E1E',
    inputPlaceholderColor: '#B0B0B080',
    inputIconColor: '#CCCCCC',
    desactivated: '#9B9B9B',
  },
  darkBlue: {
    primary: '#3674D7',
    text: '#FFFFFF',
    border: '#3674D780',
    secondary: '#3674D733',
    background: '#000000',
    icon: '#FFFFFF',
    tabIconDefault: '#A2A2A2',
    linearGradient: ['#1E2735', '#233F6E'],
    inputBackground: '#1E1E1E',
    inputPlaceholderColor: '#B0B0B080',
    inputIconColor: '#CCCCCC',
    desactivated: '#9B9B9B',
  },
  darkGreen: {
    primary: '#078566',
    text: '#FFFFFF',
    border: '#07856680',
    secondary: '#07856633',
    background: '#000000',
    icon: '#FFFFFF',
    tabIconDefault: '#A2A2A2',
    linearGradient: ['#182925', '#214839'],
    inputBackground: '#1E1E1E',
    inputPlaceholderColor: '#B0B0B080',
    inputIconColor: '#CCCCCC',
    desactivated: '#9B9B9B',
  },
};

import { Colors } from '@/constants/Colors';

/**
 * Converts hex color to HSL
 */
function hexToHsl(hex: string): { h: number; s: number; l: number } {
  // Remove hash if present
  hex = hex.replace('#', '');

  // Convert to RGB
  const r = parseInt(hex.substring(0, 2), 16) / 255;
  const g = parseInt(hex.substring(2, 4), 16) / 255;
  const b = parseInt(hex.substring(4, 6), 16) / 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h = 0;
  let s = 0;
  const l = (max + min) / 2;

  if (max === min) {
    h = s = 0; // achromatic
  } else {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

    switch (max) {
      case r:
        h = (g - b) / d + (g < b ? 6 : 0);
        break;
      case g:
        h = (b - r) / d + 2;
        break;
      case b:
        h = (r - g) / d + 4;
        break;
    }
    h /= 6;
  }

  return { h: h * 360, s: s * 100, l: l * 100 };
}

/**
 * Converts HSL to hex color
 */
function hslToHex(h: number, s: number, l: number): string {
  h /= 360;
  s /= 100;
  l /= 100;

  const hue2rgb = (p: number, q: number, t: number) => {
    if (t < 0) t += 1;
    if (t > 1) t -= 1;
    if (t < 1 / 6) return p + (q - p) * 6 * t;
    if (t < 1 / 2) return q;
    if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;
    return p;
  };

  let r, g, b;

  if (s === 0) {
    r = g = b = l; // achromatic
  } else {
    const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
    const p = 2 * l - q;
    r = hue2rgb(p, q, h + 1 / 3);
    g = hue2rgb(p, q, h);
    b = hue2rgb(p, q, h - 1 / 3);
  }

  const toHex = (c: number) => {
    const hex = Math.round(c * 255).toString(16);
    return hex.length === 1 ? '0' + hex : hex;
  };

  return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
}

/**
 * Generates a palette of colors based on the primary theme color
 * @param primaryColor - The primary color in hex format
 * @param count - Number of colors to generate
 * @param lightness - Whether the theme is light (true) or dark (false)
 * @returns Array of hex colors
 */
export function generatePrimaryColorPalette(
  primaryColor: string,
  count: number = 6,
  lightness?: 'light' | 'dark',
): string[] {
  const hsl = hexToHsl(primaryColor);
  const colors: string[] = [];

  // Determine if we should go lighter or darker based on theme
  const isLightTheme = lightness === 'light';

  for (let i = 0; i < count; i++) {
    let newHsl = { ...hsl };

    // Calculate the lightness adjustment
    const step = i * (isLightTheme ? 8 : -8); // 8% steps

    if (isLightTheme) {
      // For light themes, make colors progressively lighter
      newHsl.l = Math.min(hsl.l + step, 85);
      // Slightly reduce saturation for lighter colors
      newHsl.s = Math.max(hsl.s - step * 0.3, 30);
    } else {
      // For dark themes, make colors progressively darker
      newHsl.l = Math.max(hsl.l + step, 25);
      // Maintain or slightly increase saturation for dark themes
      newHsl.s = Math.min(hsl.s + Math.abs(step) * 0.2, 90);
    }

    colors.push(hslToHex(newHsl.h, newHsl.s, newHsl.l));
  }

  return colors;
}

/**
 * Generates theme-aware colors for pie charts and legends
 * @param currentTheme - The current app theme
 * @param count - Number of colors needed
 * @returns Array of hex colors based on the primary theme color
 */
export function generateThemeColorPalette(currentTheme: string, count: number = 6): string[] {
  const colors = Colors[currentTheme as keyof typeof Colors] || Colors.lightBlue;
  const isLightTheme = currentTheme.startsWith('light');

  return generatePrimaryColorPalette(colors.primary, count, isLightTheme ? 'light' : 'dark');
}

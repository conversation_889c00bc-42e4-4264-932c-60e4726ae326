import React, { useMemo, useState } from 'react';

import { useTheme } from '@/context/ThemeContext';
import { generateThemeColorPalette } from '@/utils/colors';

import Widget from '../Widget';
import Pie<PERSON><PERSON>, { PieDataItem } from './PieChart';
import PieChartLegend from './PieChartLegend';

interface PieChartWidgetProps<Datum> {
  title: string;
  data: Datum[];
  ready: boolean;
  renderCenterLabel: () => JSX.Element;
  renderSelectedSegment?: (entry: Datum) => JSX.Element;
  renderLegend: (entry: Datum) => JSX.Element;
  legendEntries?: Datum[];
}

export default function PieChartWidget<Datum extends PieDataItem>({
  title,
  data,
  renderCenterLabel,
  ready,
  renderSelectedSegment,
  renderLegend,
  legendEntries,
}: PieChartWidgetProps<Datum>) {
  const [selectedSegment, setSelectedSegment] = useState<Datum | null>(null);
  const { currentTheme } = useTheme();

  // Generate theme-aware colors and apply them to the data
  const coloredData = useMemo(() => {
    const colors = generateThemeColorPalette(currentTheme, data.length);
    return data.map((item, index) => ({
      ...item,
      color: colors[index % colors.length],
    }));
  }, [data, currentTheme]);

  // Also apply colors to legend entries if provided
  const coloredLegendEntries = useMemo(() => {
    if (!legendEntries) return undefined;
    const colors = generateThemeColorPalette(currentTheme, legendEntries.length);
    return legendEntries.map((item, index) => ({
      ...item,
      color: colors[index % colors.length],
    }));
  }, [legendEntries, currentTheme]);

  return (
    <Widget
      title={title}
      ready={ready}
      styles={{
        container: {
          alignItems: 'center',
          gap: 10,
        },
      }}
    >
      <PieChart
        data={coloredData}
        selectedSegment={selectedSegment}
        setSelectedSegment={setSelectedSegment}
        centerLabelComponent={renderCenterLabel}
      />
      <PieChartLegend
        renderLegend={selectedSegment ? (renderSelectedSegment ?? renderLegend) : renderLegend}
        entries={selectedSegment ? [selectedSegment] : (coloredLegendEntries ?? coloredData).slice(0, 6)}
      />
    </Widget>
  );
}

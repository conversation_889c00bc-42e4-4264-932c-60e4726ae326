import { useMemo } from 'react';

import { useTranslation } from 'react-i18next';
import { View } from 'react-native';

import { useUserProfile } from '@/common/profile';
import { ThemedText } from '@/components/global/ThemedText';
import usePortfolioQuery from '@/hooks/actor/useDepotQuery';
import { ActorService } from '@/services/actor.service';
import { CurrencyAmount, actor } from '@/signals/actor';
import { SecurityAccountSecurityType } from '@/types/secapi.types';
import { rgbToHsl } from '@/utils/strings';

import { PieDataItem } from './PieChart/PieChart';
import PieChartWidget from './PieChart/PieChartWidget';

interface PieChartEntry extends PieDataItem {
  name: string;
  amount: CurrencyAmount;
}

export default function AssetClassesWidget() {
  const { t } = useTranslation();
  const { data: performance, isLoading } = usePortfolioQuery({
    queryFn: ActorService.getPerformance,
  });
  const depot = actor.value.depot;
  const currency = useUserProfile().profile?.flags.currency;

  // entries are grouped by security type
  const entries: PieChartEntry[] = useMemo(() => {
    const entriesMap = new Map<SecurityAccountSecurityType, PieChartEntry>();
    performance?.entries.forEach(entry => {
      const sec = depot?.securities[entry.isin];
      const secType = sec?.type ?? SecurityAccountSecurityType.OTHER;
      const currentEntry = entriesMap.get(secType);
      if (currentEntry) {
        currentEntry.amount.amount += entry.amount;
        currentEntry.value += entry.amount;
      } else {
        entriesMap.set(secType, {
          id: secType.toString(),
          name: t('actor.assetClasses.secType.' + secType).toString(),
          amount: {
            amount: entry.amount,
            unit: currency,
          },
          value: entry.amount,
        });
      }
    });

    const entriesArray = [...entriesMap.values()];

    // Colors will be applied automatically by PieChartWidget using theme colors
    return entriesArray.sort((a, b) => b.amount.amount - a.amount.amount);
  }, [performance]);

  return (
    <PieChartWidget
      data={entries}
      title={t('actor.assetClasses.title')}
      ready={!isLoading}
      renderCenterLabel={() => (
        <View style={{ alignItems: 'center' }}>
          <ThemedText size={16} type="bold">
            {t('actor.division.totalAssets')}
          </ThemedText>
          <ThemedText size={24} type="bold">
            {t('currency', {
              amount: {
                amount: performance?.totalAmount ?? 0,
                unit: currency ?? 'EUR',
                options: {
                  notation: 'compact',
                },
              },
            })}
          </ThemedText>
        </View>
      )}
      renderSelectedSegment={entry => (
        <ThemedText className="flex-1 flex-wrap">
          {`${entry.name}: ${t('currency', {
            amount: {
              amount: entry.amount?.amount ?? 0,
              unit: entry.amount?.unit ?? 'EUR',
              options: {
                notation: 'compact',
              },
            },
          })} (${t('percent', { value: entry.value / (performance?.totalAmount ?? 1) })})`}
        </ThemedText>
      )}
      renderLegend={entry => <ThemedText className="flex-1 flex-wrap line-clamp-2">{`${entry.name}`}</ThemedText>}
    />
  );
}

import React from 'react';

import { ActivityIndicator, StyleProp, View, ViewStyle } from 'react-native';

import { ThemedText } from '@/components/global/ThemedText';
import { Colors } from '@/constants/Colors';
import { useTheme } from '@/context/ThemeContext';

type WidgetProps = {
  title?: string;
  children?: React.ReactNode;
  ready?: boolean;
  settings?: React.ReactNode;
  styles?: {
    container?: StyleProp<ViewStyle>;
    root?: StyleProp<ViewStyle>;
  };
};

export default function Widget({ title, children, ready, settings, styles }: WidgetProps) {
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme] || Colors.lightBlue;

  return (
    <View
      className="rounded-xl p-4 mb-6"
      style={[
        styles?.root,
        {
          backgroundColor: colors.background,
        },
      ]}
    >
      {(!!title || !!settings) && (
        <View className="mb-4 flex flex-row justify-center items-center w-full">
          <ThemedText size={28} type="outfit-semi-bold" style={{ textAlign: 'center' }}>
            {title}
          </ThemedText>
          {settings}
        </View>
      )}
      <View style={styles?.container}>{!ready ? <ActivityIndicator /> : children}</View>
    </View>
  );
}

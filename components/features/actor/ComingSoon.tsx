import React from 'react';

import { Icon } from '@rneui/themed';
import { useTranslation } from 'react-i18next';
import { StyleSheet, View } from 'react-native';

import { ThemedText } from '@/components/global/ThemedText';
import { Colors } from '@/constants/Colors';
import { useTheme } from '@/context/ThemeContext';
import { scaleFont } from '@/utils/scaler';

export default function ComingSoon({ iconName }: { iconName: string }) {
  const { t } = useTranslation();
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme] || Colors.lightBlue;

  return (
    <View style={styles.container}>
      <Icon name={iconName} type="material" size={scaleFont(40)} color={colors.primary} style={styles.icon} />
      <ThemedText h3 lightColor={colors.primary} darkColor={colors.primary} style={styles.title}>
        {t('comingSoon.title')}
      </ThemedText>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  icon: {
    marginBottom: 10,
  },
  title: {},
});

import React from 'react';

import { BlurView } from 'expo-blur';
import { LinearGradient as ExpoLinearGradient } from 'expo-linear-gradient';
import { StyleSheet, TouchableOpacity, View } from 'react-native';

import { ThemedText } from '@/components/global/ThemedText';
import { ThemedView } from '@/components/global/ThemedView';
import { Colors } from '@/constants/Colors';
import { useTheme } from '@/context/ThemeContext';
import { scaleFont } from '@/utils/scaler';

interface PerformanceWidgetProps {
  totalAssets?: number;
  totalAssetsChange?: number;
  totalAssetsChangePercentage?: number;
  totalDividends?: number;
  totalDividendsChange?: number;
  totalDividendsChangePercentage?: number;
  refundableWithholdingTax?: number;
  currency?: string;
  onClaimPress?: () => void;
}

export default function PerformanceWidget({
  totalAssets = 6304554,
  totalAssetsChange = 13.15,
  totalAssetsChangePercentage = 13.15,
  totalDividends = 94554,
  totalDividendsChange = 13.15,
  totalDividendsChangePercentage = 13.15,
  refundableWithholdingTax = 27031,
  currency = 'EUR',
  onClaimPress,
}: PerformanceWidgetProps) {
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme] || Colors.lightBlue;
  const isLightTheme = currentTheme.startsWith('light');

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (percentage: number) => {
    return `↑${percentage.toFixed(2)}%`;
  };

  return (
    <ThemedView useGradient={false} style={[styles.container, { backgroundColor: colors.background }]}>
      <ThemedText size={28} type="outfit-semi-bold" style={styles.title}>
        Performance overview
      </ThemedText>

      {/* Total Assets Section */}
      <View style={styles.sectionContainer}>
        <ThemedText style={styles.sectionLabel}>Total assets</ThemedText>
        <ExpoLinearGradient
          colors={
            currentTheme.startsWith('dark') ? ['#1d2b39', '#1a202c', '#1d2b39'] : ['#f7fafc', '#ffffff', '#f7fafc']
          }
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.gradientContainer}
        >
          <View>
            <ThemedText size={18} type="bold" style={[styles.mainValue, { color: colors.primary }]}>
              {formatCurrency(totalAssets)}
            </ThemedText>
          </View>
          <View>
            <View style={styles.changeContainer}>
              <ThemedText size={14} style={[styles.changeText, { color: colors.primary }]}>
                {formatPercentage(totalAssetsChangePercentage)}
              </ThemedText>
              <ThemedText size={14} textType="muted" style={styles.periodText}>
                over 12 months
              </ThemedText>
            </View>
          </View>
        </ExpoLinearGradient>
      </View>

      {/* Total Dividends Section */}
      <View style={styles.sectionContainer}>
        <ThemedText style={styles.sectionLabel}>Total gross dividends this year</ThemedText>
        <ExpoLinearGradient
          colors={
            currentTheme.startsWith('dark') ? ['#1d2b39', '#1a202c', '#1d2b39'] : ['#f7fafc', '#ffffff', '#f7fafc']
          }
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.gradientContainer}
        >
          <View>
            <ThemedText size={18} type="bold" style={[styles.mainValue, { color: colors.primary }]}>
              {formatCurrency(totalDividends)}
            </ThemedText>
          </View>
          <View style={styles.changeContainer}>
            <ThemedText size={14} style={[styles.changeText, { color: colors.primary }]}>
              {formatPercentage(totalDividendsChangePercentage)}
            </ThemedText>
            <ThemedText size={14} textType="muted" style={styles.periodText}>
              over 12 months
            </ThemedText>
          </View>
        </ExpoLinearGradient>
      </View>

      {/* Refundable Tax Section */}
      <View style={styles.taxSectionContainer}>
        <View style={styles.taxInfo}>
          <ThemedText style={styles.sectionLabel}>Total refundable withholding tax</ThemedText>
        </View>

        {/* Claim Button with Blur Background */}
        <ExpoLinearGradient
          colors={
            currentTheme.startsWith('dark') ? ['#1d2b39', '#1a202c', '#1d2b39'] : ['#f7fafc', '#ffffff', '#f7fafc']
          }
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.gradientContainer}
        >
          <ThemedText size={18} type="bold" style={[styles.mainValue, { color: colors.primary }]}>
            {formatCurrency(refundableWithholdingTax)}
          </ThemedText>
          <TouchableOpacity
            style={[
              styles.claimButton,
              {
                backgroundColor: colors.primary,
              },
            ]}
            onPress={onClaimPress}
            activeOpacity={0.8}
          >
            <ThemedText size={16} type="semi-bold" style={[styles.claimButtonText, { color: colors.background }]}>
              CLAIM NOW
            </ThemedText>
          </TouchableOpacity>
        </ExpoLinearGradient>
      </View>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    marginBottom: 24,
  },
  title: {
    marginBottom: 24,
    letterSpacing: -0.5,
    textAlign: 'center',
  },
  sectionContainer: {
    marginBottom: 16,
  },
  sectionLabel: {
    marginBottom: 8,
    lineHeight: 20,
  },
  gradientContainer: {
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginBottom: 8,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
  },
  mainValue: {
    letterSpacing: -1,
  },
  changeContainer: {
    flexDirection: 'column',
    alignItems: 'flex-end',
    gap: 8,
  },
  changeText: {
    fontWeight: '600',
  },
  periodText: {
    lineHeight: 20,
  },
  taxSectionContainer: {
    flexDirection: 'column',
    alignItems: 'flex-start',
  },
  taxInfo: {
    flex: 1,
  },

  blurBackground: {
    borderRadius: 20,
    overflow: 'hidden',
    padding: 2,
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  claimButton: {
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
  },
  claimButtonText: {
    letterSpacing: 0.5,
  },
});

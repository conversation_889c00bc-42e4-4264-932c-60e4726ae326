import React from 'react';

import { LinearGradient as ExpoLinearGradient } from 'expo-linear-gradient';
import { StyleSheet, View } from 'react-native';

import { ThemedText } from '@/components/global/ThemedText';
import { ThemedView } from '@/components/global/ThemedView';
import { Colors } from '@/constants/Colors';
import { useTheme } from '@/context/ThemeContext';

export interface TableRow {
  id: string;
  exDate: string;
  payDate: string;
  amount: string;
  icon?: React.ReactNode;
}

interface GenericTableProps {
  title: string;
  data: TableRow[];
  currency?: string;
}

export default function GenericTable({ title, data, currency = 'EUR' }: GenericTableProps) {
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme] || Colors.lightBlue;

  const formatDate = (dateString: string) => {
    // Assuming date format is DD.MM.YYYY
    return dateString;
  };

  const formatAmount = (amount: string, percentage?: boolean) => {
    if (percentage) {
      return `${amount}%`;
    }
    return `${amount}€`;
  };

  return (
    <ThemedView useGradient={false} style={[styles.container, { backgroundColor: colors.background }]}>
      <ThemedText size={28} type="outfit-semi-bold" style={styles.title}>
        {title}
      </ThemedText>

      {/* Table Header */}
      <View style={styles.headerContainer}>
        <ExpoLinearGradient
          colors={
            currentTheme.startsWith('dark') ? ['#1d2b39', '#1a202c', '#1d2b39'] : ['#f7fafc', '#ffffff', '#f7fafc']
          }
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={[styles.rowContainer]}
        >
          <View style={styles.headerCell}>
            <ThemedText size={14} type="semi-bold">
              Ex-date
            </ThemedText>
          </View>
          <View style={styles.headerCell}>
            <ThemedText size={14} type="semi-bold">
              Pay day
            </ThemedText>
          </View>
          <View style={[styles.headerCell, styles.amountHeader]}>
            <ThemedText size={14} type="semi-bold">
              Amount
            </ThemedText>
          </View>
        </ExpoLinearGradient>
      </View>

      {/* Table Rows */}
      {data.map((row, index) => (
        <View>
          <View style={styles.cell}>
            <ThemedText size={14} style={{ color: colors.text }}>
              {formatDate(row.exDate)}
            </ThemedText>
          </View>
          <View style={styles.cell}>
            <ThemedText size={14} style={{ color: colors.text }}>
              {formatDate(row.payDate)}
            </ThemedText>
          </View>
          <View style={[styles.cell, styles.amountCell]}>
            <View style={styles.amountContainer}>
              <ThemedText size={14} style={{ color: colors.text }}>
                {formatAmount(row.amount)}
              </ThemedText>
              {row.icon && <View style={styles.iconContainer}>{row.icon}</View>}
            </View>
          </View>
        </View>
      ))}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    marginBottom: 24,
  },
  title: {
    marginBottom: 24,
    letterSpacing: -0.5,
    textAlign: 'center',
  },
  headerContainer: {
    flexDirection: 'row',
    paddingHorizontal: 4,
    paddingVertical: 12,
    marginBottom: 8,
  },
  headerCell: {
    flex: 1,
    alignItems: 'flex-start',
  },
  amountHeader: {
    alignItems: 'flex-end',
  },
  rowContainer: {
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    marginBottom: 8,
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
  },
  lastRow: {
    marginBottom: 0,
  },
  cell: {
    flex: 1,
    alignItems: 'flex-start',
    justifyContent: 'center',
  },
  amountCell: {
    alignItems: 'flex-end',
  },
  amountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  iconContainer: {
    width: 16,
    height: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

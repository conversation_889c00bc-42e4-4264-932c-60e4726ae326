import React, { useEffect, useState } from 'react';

import { LinearGradient as ExpoLinearGradient } from 'expo-linear-gradient';
import { Dimensions, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import { LineChart } from 'react-native-gifted-charts';

import { ThemedText } from '@/components/global/ThemedText';
import { Colors } from '@/constants/Colors';
import { useTheme } from '@/context/ThemeContext';

const { width: screenWidth } = Dimensions.get('window');
const chartWidth = Math.min(screenWidth - 80, 350);

type TimePeriod = 'D' | 'W' | 'M' | 'Y' | 'ALL';
type PerformanceType = 'Absolute' | 'Return' | 'Twrror';

interface LineDataPoint {
  value: number;
}

interface PerformanceButtonProps {
  type: PerformanceType;
  isActive: boolean;
  onPress: () => void;
}

interface PeriodButtonProps {
  period: TimePeriod;
  isActive: boolean;
  onPress: () => void;
}

const PerformanceChart: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState<TimePeriod>('D');
  const [selectedPerformanceType, setSelectedPerformanceType] = useState<PerformanceType>('Absolute');
  const { currentTheme } = useTheme();
  const themeColors = Colors[currentTheme] || Colors.lightBlue;

  // Generate sample performance data
  const generatePerformanceData = (period: TimePeriod, performanceType: PerformanceType): LineDataPoint[] => {
    const getDataForPeriod = () => {
      let baseValue: number;
      let growthFactor: number;
      let dataPoints: number;

      // Set base values based on performance type
      switch (performanceType) {
        case 'Absolute':
          baseValue = 100;
          growthFactor = period === 'ALL' ? 2.5 : 1.5;
          break;
        case 'Return':
          baseValue = 0;
          growthFactor = period === 'ALL' ? 25 : 15; // Percentage returns
          break;
        case 'Twrror':
          baseValue = 50;
          growthFactor = period === 'ALL' ? 1.8 : 1.3;
          break;
        default:
          baseValue = 100;
          growthFactor = 1.5;
      }

      switch (period) {
        case 'D':
          // Daily data for last 30 days
          dataPoints = 30;
          return Array.from({ length: dataPoints }, (_, i) => {
            const progress = i / (dataPoints - 1);
            const trend = baseValue + (baseValue * growthFactor - baseValue) * progress;
            const variation = (Math.random() - 0.5) * (baseValue * 0.1);
            const value = Math.max(baseValue * 0.8, trend + variation);

            return {
              value: Math.round(value * 100) / 100,
            };
          });

        case 'W':
          // Weekly data for last 12 weeks
          dataPoints = 12;
          return Array.from({ length: dataPoints }, (_, i) => {
            const progress = i / (dataPoints - 1);
            const trend = baseValue + (baseValue * growthFactor - baseValue) * progress;
            const variation = (Math.random() - 0.5) * (baseValue * 0.15);
            const value = Math.max(baseValue * 0.7, trend + variation);

            return {
              value: Math.round(value * 100) / 100,
            };
          });

        case 'M':
          // Monthly data for last 12 months
          const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
          return months.map((month, i) => {
            const progress = i / (months.length - 1);
            const trend = baseValue + (baseValue * growthFactor - baseValue) * progress;
            const variation = (Math.random() - 0.5) * (baseValue * 0.2);
            const value = Math.max(baseValue * 0.6, trend + variation);

            return {
              value: Math.round(value * 100) / 100,
            };
          });

        case 'Y':
          // Yearly data for last 8 years
          const currentYear = new Date().getFullYear();
          dataPoints = 8;
          return Array.from({ length: dataPoints }, (_, i) => {
            const progress = i / (dataPoints - 1);
            const trend = baseValue + (baseValue * growthFactor - baseValue) * progress;
            const variation = (Math.random() - 0.5) * (baseValue * 0.25);
            const value = Math.max(baseValue * 0.5, trend + variation);

            return {
              value: Math.round(value * 100) / 100,
            };
          });

        case 'ALL':
        default:
          // All time data - showing growth trend over years
          const years = ['2018', '2019', '2020', '2021', '2022', '2023', '2024', '2025'];
          return years.map((year, i) => {
            const progress = i / (years.length - 1);
            const trend = baseValue + (baseValue * growthFactor - baseValue) * progress;
            const variation = (Math.random() - 0.5) * (baseValue * 0.3);
            const value = Math.max(baseValue * 0.4, trend + variation);

            return {
              value: Math.round(value * 100) / 100,
            };
          });
      }
    };

    return getDataForPeriod();
  };

  const [chartData, setChartData] = useState<LineDataPoint[]>(
    generatePerformanceData(selectedPeriod, selectedPerformanceType),
  );
  const [maxValue, setMaxValue] = useState<number>(250);
  const [minValue, setMinValue] = useState<number>(0);

  useEffect(() => {
    const newData = generatePerformanceData(selectedPeriod, selectedPerformanceType);
    setChartData(newData);

    // Calculate max and min values for the chart
    const values = newData.map(item => item.value);
    const max = Math.max(...values);
    const min = Math.min(...values);

    setMaxValue(Math.ceil(max * 1.1)); // Add 10% padding
    setMinValue(Math.floor(min * 0.9)); // Subtract 10% padding
  }, [selectedPeriod, selectedPerformanceType, themeColors.primary]);

  const periods: TimePeriod[] = ['D', 'W', 'M', 'Y', 'ALL'];
  const performanceTypes: PerformanceType[] = ['Absolute', 'Return', 'Twrror'];

  const PerformanceButton: React.FC<PerformanceButtonProps> = ({ type, isActive, onPress }) => (
    <TouchableOpacity
      style={[
        styles.performanceButton,
        { backgroundColor: isActive ? themeColors.border : themeColors.inputBackground },
      ]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <ThemedText type={isActive ? 'semi-bold' : 'regular'}>{type}</ThemedText>
    </TouchableOpacity>
  );

  const PeriodButton: React.FC<PeriodButtonProps> = ({ period, isActive, onPress }) => (
    <TouchableOpacity
      style={[
        styles.periodButton,
        isActive && [styles.activePeriodButton, { borderWidth: 2, borderColor: themeColors.primary }],
      ]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <ThemedText size={15} type="semi-bold">
        {period}
      </ThemedText>
    </TouchableOpacity>
  );

  // Get current value and format based on performance type
  const getCurrentValue = () => {
    const currentValue = chartData[chartData.length - 1]?.value || 0;
    switch (selectedPerformanceType) {
      case 'Return':
        return `${currentValue.toFixed(1)}%`;
      case 'Absolute':
      case 'Twrror':
      default:
        return `$${currentValue.toFixed(0)}`;
    }
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={[styles.card, { backgroundColor: themeColors.background }]}>
        {/* Header */}
        <View style={styles.header}>
          <ThemedText
            size={28}
            type="outfit-semi-bold"
            style={{ textAlign: 'center', marginBottom: 6, letterSpacing: -0.5 }}
          >
            Performance
          </ThemedText>

          {/* Performance Type Selector */}
          <View style={styles.performanceSelector}>
            {performanceTypes.map(type => (
              <PerformanceButton
                key={type}
                type={type}
                isActive={selectedPerformanceType === type}
                onPress={() => setSelectedPerformanceType(type)}
              />
            ))}
          </View>
        </View>

        {/* Chart Container */}
        <View style={styles.chartContainer}>
          <LineChart
            data={chartData}
            width={chartWidth - 40}
            height={200}
            spacing={chartWidth / Math.max(chartData.length - 1, 1)}
            hideRules={true}
            hideYAxisText={true}
            hideAxesAndRules={true}
            yAxisThickness={0}
            isAnimated={true}
            animationDuration={1200}
            maxValue={maxValue}
            mostNegativeValue={minValue}
            color={themeColors.primary}
            thickness={3}
            startFillColor={themeColors.primary + '40'}
            endFillColor={themeColors.primary + '10'}
            startOpacity={0.8}
            endOpacity={0.1}
            areaChart={true}
            curved={true}
            hideDataPoints={true}
            backgroundColor="transparent"
            showScrollIndicator={false}
            scrollToEnd={false}
            initialSpacing={10}
            endSpacing={10}
            // Disable focus interaction
            focusEnabled={false}
          />
        </View>

        {/* Period Selector */}
        <View style={styles.periodSelectorContainer}>
          <View style={styles.periodButtonsContainer}>
            {periods.map(period => (
              <PeriodButton
                key={period}
                period={period}
                isActive={selectedPeriod === period}
                onPress={() => setSelectedPeriod(period)}
              />
            ))}
          </View>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    marginBottom: 24,
  },
  card: {
    borderRadius: 24,
    padding: 28,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.12,
    shadowRadius: 16,
    elevation: 12,
  },
  header: {
    marginBottom: 24,
  },
  performanceSelector: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 20,
    paddingHorizontal: 20,
  },
  performanceButton: {
    paddingHorizontal: 20,
    paddingVertical: 8,
    marginHorizontal: 8,
    borderRadius: 60,
  },
  currentValueContainer: {
    marginBottom: 32,
    alignItems: 'center',
  },
  chartContainer: {
    alignItems: 'center',
    marginBottom: 40,
    paddingHorizontal: 10,
  },
  periodSelectorContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  periodButtonsContainer: {
    flexDirection: 'row',
    borderRadius: 28,
    width: '100%',
    justifyContent: 'space-between',
  },
  periodButton: {
    paddingHorizontal: 18,
    borderRadius: 22,
    paddingVertical: 3,
  },
  activePeriodButton: {
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.2,
    shadowRadius: 6,
    elevation: 6,
  },
});

export default PerformanceChart;

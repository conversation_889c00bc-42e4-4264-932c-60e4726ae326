import React, { useEffect, useState } from 'react';

import { LinearGradient as ExpoLinearGradient } from 'expo-linear-gradient';
import { Dimensions, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import { BarChart } from 'react-native-gifted-charts';

import { ThemedText } from '@/components/global/ThemedText';
import { Colors } from '@/constants/Colors';
import { useTheme } from '@/context/ThemeContext';

const { width: screenWidth } = Dimensions.get('window');
const chartWidth = Math.min(screenWidth - 80, 350);

type TimePeriod = 'D' | 'W' | 'M' | 'Y' | 'ALL';

interface BarDataPoint {
  value: number;
  label?: string;
  frontColor?: string;
  gradientColor?: string;
  spacing?: number;
  labelWidth?: number;
  labelTextStyle?: object;
}

interface StatRowProps {
  label: string;
  value: string;
  valueColor: string;
  fontSize?: number;
}

interface PeriodButtonProps {
  period: TimePeriod;
  isActive: boolean;
  onPress: () => void;
}

const DividendsChart: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState<TimePeriod>('ALL');
  const { currentTheme } = useTheme();
  const themeColors = Colors[currentTheme] || Colors.lightBlue;

  // Generate sample dividend data
  const generateDividendData = (period: TimePeriod): BarDataPoint[] => {
    const data: BarDataPoint[] = [];

    const getDataForPeriod = () => {
      switch (period) {
        case 'D':
          // Daily data for last 12 days - optimized for alignment
          return Array.from({ length: 12 }, (_, i) => {
            const value = Math.random() * 15 + 5; // $5-20 range
            return {
              value,
              label: `${i + 1}`,
              frontColor: themeColors.primary,
              gradientColor: themeColors.primary + '40',
              spacing: 25, // Optimized spacing for alignment
              labelWidth: 25, // Smaller width for day numbers
            };
          });

        case 'W':
          // Weekly data for last 8 weeks - optimized for alignment
          return Array.from({ length: 8 }, (_, i) => {
            const value = Math.random() * 50 + 20; // $20-70 range
            return {
              value,
              label: `W${i + 1}`,
              frontColor: themeColors.primary,
              gradientColor: themeColors.primary + '40',
              spacing: 30, // Optimized spacing for alignment
              labelWidth: 30, // Adequate width for W1, W2, etc.
            };
          });

        case 'M':
          // Monthly data for last 12 months
          const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
          return months.map((month, i) => {
            const value = Math.random() * 100 + 50; // $50-150 range
            return {
              value,
              label: month,
              frontColor: themeColors.primary,
              gradientColor: themeColors.primary + '40',
              spacing: 28, // Optimized spacing for month alignment
              labelWidth: 28, // Perfect width for 3-char month names
            };
          });

        case 'Y':
          // Yearly data for last 10 years - more data for scrolling
          const currentYear = new Date().getFullYear();
          return Array.from({ length: 10 }, (_, i) => {
            const year = currentYear - 9 + i;
            const value = Math.random() * 500 + 200; // $200-700 range
            return {
              value,
              label: year.toString(),
              frontColor: themeColors.primary,
              gradientColor: themeColors.primary + '40',
              spacing: 35, // Optimized spacing for year alignment
              labelWidth: 35, // Perfect width for 4-digit years
            };
          });

        case 'ALL':
        default:
          // All time data - showing growth trend over years with more data points
          const years = ['2015', '2016', '2017', '2018', '2019', '2020', '2021', '2022', '2023', '2024', '2025'];
          return years.map((year, i) => {
            // Create ascending trend with some variation
            const baseValue = 80 + i * 40; // Base growth
            const variation = (Math.random() - 0.5) * 30; // ±15 variation
            const value = Math.max(50, baseValue + variation);

            return {
              value,
              label: year,
              frontColor: themeColors.primary,
              gradientColor: themeColors.primary + '40',
              spacing: i === years.length - 1 ? 0 : 35, // Optimized spacing for year alignment
              labelWidth: 35, // Perfect width for 4-digit years
            };
          });
      }
    };

    return getDataForPeriod();
  };

  const [chartData, setChartData] = useState<BarDataPoint[]>(generateDividendData(selectedPeriod));
  const [maxValue, setMaxValue] = useState<number>(400);

  useEffect(() => {
    const newData = generateDividendData(selectedPeriod);
    setChartData(newData);

    // Calculate max value for the chart
    const max = Math.max(...newData.map(item => item.value));
    setMaxValue(Math.ceil(max * 1.2)); // Add 20% padding
  }, [selectedPeriod, themeColors.primary]);

  const periods: TimePeriod[] = ['D', 'W', 'M', 'Y', 'ALL'];

  const PeriodButton: React.FC<PeriodButtonProps> = ({ period, isActive, onPress }) => (
    <TouchableOpacity
      style={[
        styles.periodButton,
        isActive && [styles.activePeriodButton, { borderWidth: 2, borderColor: themeColors.primary }],
      ]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <ThemedText size={15} type="semi-bold">
        {period}
      </ThemedText>
    </TouchableOpacity>
  );

  // Calculate total dividends for display
  const totalDividends = chartData.reduce((sum, item) => sum + item.value, 0);

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={[styles.card, { backgroundColor: themeColors.background }]}>
        {/* Header */}
        <View style={styles.header}>
          <ThemedText
            size={28}
            type="outfit-semi-bold"
            style={{ textAlign: 'center', marginBottom: 6, letterSpacing: -0.5 }}
          >
            Dividends
          </ThemedText>
          <ExpoLinearGradient
            colors={
              currentTheme.startsWith('dark') ? ['#1d2b39', '#1a202c', '#1d2b39'] : ['#f7fafc', '#ffffff', '#f7fafc']
            }
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.subtitleContainer}
          >
            <ThemedText
              size={14}
              style={{
                textAlign: 'center',
                opacity: 0.7,
                paddingVertical: 4,
              }}
            >
              Swipe chart to view info
            </ThemedText>
          </ExpoLinearGradient>
        </View>

        {/* Chart Container with Y-axis labels */}
        <View style={styles.chartContainerWrapper}>
          {/* Y-axis labels */}
          <View style={styles.yAxisLabels}>
            <ThemedText size={13} type="semi-bold" style={{ color: themeColors.primary }}>
              ${Math.round(maxValue)}
            </ThemedText>
            <View style={styles.yAxisSpacer} />
            <ThemedText size={13} type="semi-bold" style={{ color: themeColors.primary }}>
              ${Math.round(maxValue / 2)}
            </ThemedText>
            <View style={styles.yAxisSpacer} />
            <ThemedText size={13} type="semi-bold" style={{ color: themeColors.primary }}>
              $0
            </ThemedText>
          </View>

          {/* Chart */}
          <View style={styles.chartContainer}>
            <BarChart
              data={chartData}
              width={chartWidth - 80}
              height={200}
              barWidth={4} // Set to 4px as requested
              spacing={chartData[0]?.spacing || 45} // Use spacing from data with larger fallback
              roundedTop={true}
              roundedBottom={false}
              hideRules={true}
              hideYAxisText={true}
              hideAxesAndRules={true}
              yAxisThickness={0}
              xAxisThickness={0}
              isAnimated={true}
              animationDuration={1500}
              maxValue={maxValue}
              frontColor={themeColors.primary}
              gradientColor={themeColors.primary + '40'}
              showGradient={true}
              backgroundColor="transparent"
              showValuesAsTopLabel={false}
              xAxisLabelTextStyle={{
                color: themeColors.primary,
                fontSize: 11,
                fontWeight: '600',
                textAlign: 'center',
                marginLeft: -2, // Fine-tune label positioning
              }}
              labelWidth={chartData[0]?.labelWidth || 50} // Use labelWidth from data
              showScrollIndicator={false}
              scrollToEnd={selectedPeriod === 'ALL'}
              disableScroll={false} // Always allow scrolling
              initialSpacing={20} // Increased initial spacing for better alignment
              barBorderRadius={2} // Subtle rounding for thin bars
              stepValue={maxValue / 4} // Better y-axis distribution
              noOfSections={4} // Clean y-axis sections
            />
          </View>
        </View>

        {/* Period Selector */}
        <View style={styles.periodSelector}>
          <View style={styles.periodButtonsContainer}>
            {periods.map(period => (
              <PeriodButton
                key={period}
                period={period}
                isActive={selectedPeriod === period}
                onPress={() => setSelectedPeriod(period)}
              />
            ))}
          </View>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    marginBottom: 24,
  },
  card: {
    borderRadius: 24,
    padding: 28,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.12,
    shadowRadius: 16,
    elevation: 12,
  },
  header: {
    marginBottom: 36,
  },
  subtitleContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  statsContainer: {
    gap: 16,
  },
  statRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  chartContainerWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 40,
    height: 240,
  },
  yAxisLabels: {
    justifyContent: 'space-between',
    height: 200,
    marginRight: 16,
    paddingTop: 12,
    width: 40,
  },
  yAxisSpacer: {
    flex: 1,
  },
  chartContainer: {
    flex: 1,
    alignItems: 'flex-start',
    paddingLeft: 10,
  },
  periodSelector: {
    alignItems: 'center',
    marginBottom: 40,
  },
  periodButtonsContainer: {
    flexDirection: 'row',
    borderRadius: 28,
    width: '100%',
    justifyContent: 'space-between',
  },
  periodButton: {
    paddingHorizontal: 18,
    borderRadius: 22,
    paddingVertical: 3,
  },
  activePeriodButton: {
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.2,
    shadowRadius: 6,
    elevation: 6,
  },
  gradientRowContainer: {
    width: '100%',
  },
});

export default DividendsChart;

import React, { useEffect, useState } from 'react';

import { LinearGradient as ExpoLinearGradient } from 'expo-linear-gradient';
import { Dimensions, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import { LineChart } from 'react-native-gifted-charts';

import { ThemedText } from '@/components/global/ThemedText';
import { Colors } from '@/constants/Colors';
import { useTheme } from '@/context/ThemeContext';

const { width: screenWidth } = Dimensions.get('window');
const chartWidth = Math.min(screenWidth - 80, 350);

type TimePeriod = 'D' | 'W' | 'M' | 'Y' | 'ALL';

interface DataPoint {
  value: number;
  dataPointText?: string;
}

interface ChartDatasets {
  main: DataPoint[];
  ftse: DataPoint[];
  background: DataPoint[];
  nvidia: DataPoint[];
}

interface StatRowProps {
  label: string;
  value: string;
  valueColor: string;
  fontSize?: number;
}

interface PeriodButtonProps {
  period: TimePeriod;
  isActive: boolean;
  onPress: () => void;
}

const InvestmentChart: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState<TimePeriod>('Y');
  const { currentTheme } = useTheme();
  const themeColors = Colors[currentTheme] || Colors.lightBlue;

  // Generate sample data points
  const generateChartData = (): ChartDatasets => {
    const points = 50;
    const datasets: ChartDatasets = {
      main: [],
      ftse: [],
      background: [],
      nvidia: [],
    };

    for (let i = 0; i <= points; i++) {
      const progress = i / points;

      // Main investment line (strongest performance)
      const mainBase = 0.1;
      const mainGrowth = 0.3 * progress;
      const mainNoise = Math.sin(progress * 12) * 0.02 + (Math.random() - 0.5) * 0.01;
      const mainValue = mainBase + mainGrowth + mainNoise;

      datasets.main.push({
        value: Math.max(0.05, mainValue),
        dataPointText: `$${mainValue.toFixed(3)}`,
      });

      // FTSE line (moderate performance)
      const ftseBase = 0.08;
      const ftseGrowth = 0.22 * progress;
      const ftseNoise = Math.sin(progress * 8) * 0.015 + (Math.random() - 0.5) * 0.008;
      const ftseValue = ftseBase + ftseGrowth + ftseNoise;

      datasets.ftse.push({
        value: Math.max(0.04, ftseValue),
        dataPointText: `$${ftseValue.toFixed(3)}`,
      });

      // Background line (lowest performance)
      const bgBase = 0.06;
      const bgGrowth = 0.15 * progress;
      const bgNoise = Math.sin(progress * 6) * 0.01 + (Math.random() - 0.5) * 0.005;
      const bgValue = bgBase + bgGrowth + bgNoise;

      datasets.background.push({
        value: Math.max(0.03, bgValue),
        dataPointText: `$${bgValue.toFixed(3)}`,
      });

      // Nvidia line (volatile, good growth)
      const nvidiaBase = 0.09;
      const nvidiaGrowth = 0.28 * progress;
      const nvidiaNoise = Math.sin(progress * 15) * 0.025 + (Math.random() - 0.5) * 0.012;
      const nvidiaValue = nvidiaBase + nvidiaGrowth + nvidiaNoise;

      datasets.nvidia.push({
        value: Math.max(0.04, nvidiaValue),
        dataPointText: `$${nvidiaValue.toFixed(3)}`,
      });
    }

    return datasets;
  };

  const [chartData, setChartData] = useState<ChartDatasets>(generateChartData());

  useEffect(() => {
    setChartData(generateChartData());
  }, [selectedPeriod]);

  const periods: TimePeriod[] = ['D', 'W', 'M', 'Y', 'ALL'];

  const StatRow: React.FC<StatRowProps> = ({ label, value, valueColor, fontSize = 18 }) => (
    <View style={styles.statRow}>
      <ThemedText>{label}</ThemedText>
      <ThemedText type="bold">{value}</ThemedText>
    </View>
  );

  const StatRowWithGradient: React.FC<StatRowProps> = ({ label, value, valueColor, fontSize = 18 }) => (
    <ExpoLinearGradient
      colors={currentTheme.startsWith('dark') ? ['#1d2b39', '#1a202c', '#1d2b39'] : ['#f7fafc', '#ffffff', '#f7fafc']}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 0 }}
      style={styles.gradientRowContainer}
    >
      <View style={styles.statRow}>
        <ThemedText>{label}</ThemedText>
        <ThemedText type="bold">{value}</ThemedText>
      </View>
    </ExpoLinearGradient>
  );

  const PeriodButton: React.FC<PeriodButtonProps> = ({ period, isActive, onPress }) => (
    <TouchableOpacity
      style={[
        styles.periodButton,
        isActive && [styles.activePeriodButton, { borderWidth: 2, borderColor: themeColors.primary }],
      ]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <ThemedText size={15} type="semi-bold">
        {period}
      </ThemedText>
    </TouchableOpacity>
  );

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={[styles.card, { backgroundColor: themeColors.background }]}>
        {/* Header */}
        <View style={styles.header}>
          <ThemedText
            size={28}
            type="outfit-semi-bold"
            style={{ textAlign: 'center', marginBottom: 6, letterSpacing: -0.5 }}
          >
            Comparison
          </ThemedText>
          <ExpoLinearGradient
            colors={
              currentTheme.startsWith('dark') ? ['#1d2b39', '#1a202c', '#1d2b39'] : ['#f7fafc', '#ffffff', '#f7fafc']
            }
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.dateGradientContainer}
          >
            <ThemedText style={styles.dateGradientText}>{new Date().toLocaleDateString('en-GB')}</ThemedText>
          </ExpoLinearGradient>

          {/* Stats */}
          <View style={styles.statsContainer}>
            <StatRow label="Share profits" value="€30.02" valueColor="#38a169" fontSize={18} />
            <StatRowWithGradient label="Investment value" value="€15,006.65" valueColor="#1a202c" fontSize={22} />
          </View>
        </View>

        {/* Chart Container with Y-axis labels */}
        <View style={styles.chartContainerWrapper}>
          {/* Y-axis labels */}
          <View style={styles.yAxisLabels}>
            <ThemedText size={13} type="semi-bold" style={{ color: themeColors.primary }}>
              $0.4
            </ThemedText>
            <View style={styles.yAxisSpacer} />
            <ThemedText size={13} type="semi-bold" style={{ color: themeColors.primary }}>
              $0.2
            </ThemedText>
          </View>

          {/* Chart */}
          <View style={styles.chartContainer}>
            <LineChart
              data={chartData.main}
              data2={chartData.nvidia}
              data3={chartData.ftse}
              data4={chartData.background}
              width={chartWidth - 60} // Account for Y-axis labels
              height={200}
              color1={themeColors.primary}
              color2={themeColors.primary + '80'}
              color3={themeColors.primary + '60'}
              color4={themeColors.primary + '30'}
              thickness1={3}
              thickness2={2}
              thickness3={2}
              thickness4={1}
              areaChart1={true}
              areaChart2={true}
              areaChart3={true}
              areaChart4={true}
              startFillColor1={themeColors.primary + '40'}
              startFillColor2={themeColors.primary + '20'}
              startFillColor3={themeColors.primary + '25'}
              startFillColor4={themeColors.primary + '20'}
              endFillColor1={themeColors.primary + '05'}
              endFillColor2={themeColors.primary + '05'}
              endFillColor3={themeColors.primary + '05'}
              endFillColor4={themeColors.primary + '02'}
              startOpacity1={0.4}
              startOpacity2={0.2}
              startOpacity3={0.25}
              startOpacity4={0.1}
              endOpacity1={0.05}
              endOpacity2={0.05}
              endOpacity3={0.05}
              endOpacity4={0.02}
              curved={true}
              isAnimated={true}
              animationDuration={2500}
              hideDataPoints1={true}
              hideDataPoints2={true}
              hideDataPoints3={true}
              hideDataPoints4={true}
              hideAxesAndRules={true}
              hideYAxisText={true}
              showVerticalLines={false}
              backgroundColor="transparent"
              showDataPointOnFocus={true}
              showStripOnFocus={true}
              showTextOnFocus={true}
              stripColor={themeColors.primary + '40'}
              stripOpacity={0.4}
              stripWidth={2}
              textShiftY={-10}
              textShiftX={-10}
              textColor1={themeColors.primary}
              textFontSize1={12}
              focusEnabled={true}
              dataPointsColor1="#000"
              dataPointsRadius1={6}
              dataPointsWidth1={3}
              dataPointsColor2="#fff"
              dataPointsRadius2={3}
              maxValue={0.5}
              noOfSections={2}
            />
          </View>
        </View>

        {/* Period Selector */}
        <View style={styles.periodSelector}>
          <View style={styles.periodButtonsContainer}>
            {periods.map(period => (
              <PeriodButton
                key={period}
                period={period}
                isActive={selectedPeriod === period}
                onPress={() => setSelectedPeriod(period)}
              />
            ))}
          </View>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    marginBottom: 24,
  },
  card: {
    borderRadius: 24,
    padding: 28,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.12,
    shadowRadius: 16,
    elevation: 12,
  },
  header: {
    marginBottom: 36,
  },
  statsContainer: {
    gap: 16,
  },
  statRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  chartContainerWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 40,
    height: 220,
  },
  yAxisLabels: {
    justifyContent: 'space-between',
    height: 200,
    marginRight: 16,
    paddingTop: 12,
    width: 32,
  },
  yAxisSpacer: {
    flex: 1,
  },
  chartContainer: {
    flex: 1,
    alignItems: 'center',
  },
  periodSelector: {
    alignItems: 'center',
    marginBottom: 40,
  },
  periodButtonsContainer: {
    flexDirection: 'row',
    borderRadius: 28,
    width: '100%',
    justifyContent: 'space-between',
  },
  periodButton: {
    paddingHorizontal: 18,
    borderRadius: 22,
    paddingVertical: 3,
  },
  activePeriodButton: {
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.2,
    shadowRadius: 6,
    elevation: 6,
  },
  gradientRowContainer: {
    width: '100%',
  },
  dateGradientContainer: {
    marginBottom: 16,
    marginTop: 10,
    width: '100%',
    alignItems: 'flex-start',
  },
  dateGradientText: {
    textAlign: 'left',
  },
});

export default InvestmentChart;

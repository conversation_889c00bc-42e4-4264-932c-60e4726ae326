import { useSignals } from '@preact/signals-react/runtime';
import { useTranslation } from 'react-i18next';
import { LogBox } from 'react-native';
import { RefreshControl } from 'react-native-gesture-handler';

import { SafeAreaView, ScrollScreen } from '@/components/base';
import {
  AssetClassesWidget,
  CalendarWidget,
  CompanyQuotesWidget,
  DivisionWidget,
  PortfolioSelector,
  PortfolioStatsWidget,
  SimulationWidget,
} from '@/components/features/actor';
import ValueDivisionExampleWidget from '@/components/features/actor/ValueDivisionExampleWidget';
import { ThemedText } from '@/components/global/ThemedText';
import DividendsChart from '@/components/widgets/DividendsChart';
import GenericTable from '@/components/widgets/GenericTable';
import InvestmentChart from '@/components/widgets/InvestmentChart';
import PerformanceChart from '@/components/widgets/PerformanceChart';
import PerformanceWidget from '@/components/widgets/PerformanceWidget';
import usePortfolioQuery from '@/hooks/actor/useDepotQuery';
import useInitializeActor from '@/hooks/useInitializeActor';
import { ActorService } from '@/services/actor.service';

LogBox.ignoreLogs(['Image source "null" doesn\'t exist', 'No stops in gradient']);

export default function Analyze() {
  const { t } = useTranslation();
  const { isFetching, refetch } = useInitializeActor();

  useSignals();

  return (
    <SafeAreaView>
      <ScrollScreen
        refreshControl={
          <RefreshControl
            refreshing={isFetching}
            onRefresh={() => {
              refetch();
            }}
          />
        }
      >
        <ThemedText size={24} type="outfit-semi-bold" className="mb-5 mx-1.5">
          {t('common.tabs.dashboard')}
        </ThemedText>

        <PortfolioSelector style={{ marginBottom: 20 }} />
        <InvestmentChart />
        <DividendsChart />
        <PerformanceChart />
        <PerformanceWidget />
        <GenericTable title="test" data={[]} />
        <DivisionWidget />
        <CompanyQuotesWidget
          queryKey={range => ['getPerformanceQuotes', range.toString()]}
          useQuery={usePortfolioQuery}
          queryFn={range => ActorService.getPerformanceQuotes(range)}
          enableTWROR
        />

        <ValueDivisionExampleWidget />

        <SimulationWidget />
        <PortfolioStatsWidget />
        <AssetClassesWidget />
        <CalendarWidget />
      </ScrollScreen>
    </SafeAreaView>
  );
}
